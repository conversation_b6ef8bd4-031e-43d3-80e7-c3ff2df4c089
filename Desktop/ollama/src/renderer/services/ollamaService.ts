import { OllamaModel, ChatMessage } from '../types'

export class OllamaService {
  private baseUrl: string
  private isConnected: boolean = false

  constructor(baseUrl: string = 'http://localhost:11434') {
    this.baseUrl = baseUrl
  }

  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`)
      this.isConnected = response.ok
      return this.isConnected
    } catch (error) {
      console.error('Failed to connect to Ollama:', error)
      this.isConnected = false
      return false
    }
  }

  async getModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`)
      if (!response.ok) {
        throw new Error('Failed to fetch models')
      }
      
      const data = await response.json()
      return data.models.map((model: any) => ({
        name: model.name,
        id: model.name,
        size: this.formatSize(model.size),
        modified: new Date(model.modified_at).toLocaleDateString()
      }))
    } catch (error) {
      console.error('Error fetching models:', error)
      return []
    }
  }

  async generateResponse(
    model: string, 
    prompt: string, 
    context?: string,
    onStream?: (chunk: string) => void
  ): Promise<string> {
    try {
      const fullPrompt = context ? `${context}\n\n${prompt}` : prompt
      
      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          prompt: fullPrompt,
          stream: !!onStream
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate response')
      }

      if (onStream && response.body) {
        return this.handleStreamResponse(response, onStream)
      } else {
        const data = await response.json()
        return data.response
      }
    } catch (error) {
      console.error('Error generating response:', error)
      throw error
    }
  }

  async chatCompletion(
    model: string,
    messages: ChatMessage[],
    onStream?: (chunk: string) => void
  ): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          stream: !!onStream
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get chat completion')
      }

      if (onStream && response.body) {
        return this.handleStreamResponse(response, onStream)
      } else {
        const data = await response.json()
        return data.message.content
      }
    } catch (error) {
      console.error('Error in chat completion:', error)
      throw error
    }
  }

  async analyzeCode(
    model: string,
    code: string,
    language: string,
    task: 'explain' | 'review' | 'optimize' | 'debug' | 'comment'
  ): Promise<string> {
    const prompts = {
      explain: `Please explain this ${language} code in detail:\n\n\`\`\`${language}\n${code}\n\`\`\``,
      review: `Please review this ${language} code and suggest improvements:\n\n\`\`\`${language}\n${code}\n\`\`\``,
      optimize: `Please optimize this ${language} code for better performance:\n\n\`\`\`${language}\n${code}\n\`\`\``,
      debug: `Please help debug this ${language} code and identify potential issues:\n\n\`\`\`${language}\n${code}\n\`\`\``,
      comment: `Please add appropriate comments to this ${language} code:\n\n\`\`\`${language}\n${code}\n\`\`\``
    }

    return this.generateResponse(model, prompts[task])
  }

  async generateCode(
    model: string,
    description: string,
    language: string,
    context?: string
  ): Promise<string> {
    const prompt = `Generate ${language} code for the following requirement:

${description}

${context ? `Context:\n${context}\n` : ''}

Please provide clean, well-commented code with proper error handling.`

    return this.generateResponse(model, prompt)
  }

  async suggestCompletion(
    model: string,
    code: string,
    language: string,
    cursorPosition: { line: number; column: number }
  ): Promise<string[]> {
    const prompt = `Given this ${language} code, suggest possible completions for the cursor position at line ${cursorPosition.line}, column ${cursorPosition.column}:

\`\`\`${language}
${code}
\`\`\`

Provide 3-5 short, relevant code completions.`

    try {
      const response = await this.generateResponse(model, prompt)
      // Parse the response to extract suggestions
      return this.parseCompletionSuggestions(response)
    } catch (error) {
      console.error('Error getting code suggestions:', error)
      return []
    }
  }

  private async handleStreamResponse(
    response: Response,
    onStream: (chunk: string) => void
  ): Promise<string> {
    const reader = response.body!.getReader()
    const decoder = new TextDecoder()
    let fullResponse = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          try {
            const data = JSON.parse(line)
            if (data.response) {
              fullResponse += data.response
              onStream(data.response)
            }
            if (data.message?.content) {
              fullResponse += data.message.content
              onStream(data.message.content)
            }
          } catch (e) {
            // Ignore JSON parse errors for incomplete chunks
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    return fullResponse
  }

  private parseCompletionSuggestions(response: string): string[] {
    // Simple parsing - in a real implementation, you'd want more sophisticated parsing
    const lines = response.split('\n').filter(line => line.trim())
    return lines.slice(0, 5).map(line => line.trim())
  }

  private formatSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  getConnectionStatus(): boolean {
    return this.isConnected
  }

  setBaseUrl(url: string): void {
    this.baseUrl = url
    this.isConnected = false
  }
}

// Export a singleton instance
export const ollamaService = new OllamaService()
