import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import FileTree from './components/FileTree'
import CodeEditor from './components/CodeEditor'
import AIAssistant from './components/AIAssistant'
import { FileItem, OpenFile } from './types'

const AppContainer = styled.div`
  display: flex;
  height: 100vh;
  background-color: #1e1e1e;
  color: #d4d4d4;
`

const LeftPanel = styled.div`
  width: 300px;
  min-width: 200px;
  max-width: 500px;
  background-color: #252526;
  border-right: 1px solid #3e3e42;
  resize: horizontal;
  overflow: hidden;
`

const MiddlePanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
`

const RightPanel = styled.div`
  width: 400px;
  min-width: 300px;
  max-width: 600px;
  background-color: #252526;
  border-left: 1px solid #3e3e42;
  resize: horizontal;
  overflow: hidden;
`

const App: React.FC = () => {
  const [currentFolder, setCurrentFolder] = useState<string>('')
  const [openFiles, setOpenFiles] = useState<OpenFile[]>([])
  const [activeFileIndex, setActiveFileIndex] = useState<number>(-1)
  const [fileTree, setFileTree] = useState<FileItem[]>([])

  useEffect(() => {
    // 监听文件夹打开事件
    window.electronAPI.onFolderOpened((folderPath: string) => {
      setCurrentFolder(folderPath)
      loadFileTree(folderPath)
    })

    // 监听新建文件事件
    window.electronAPI.onNewFile(() => {
      createNewFile()
    })

    // 监听保存文件事件
    window.electronAPI.onSaveFile(() => {
      saveCurrentFile()
    })

    return () => {
      window.electronAPI.removeAllListeners('folder-opened')
      window.electronAPI.removeAllListeners('new-file')
      window.electronAPI.removeAllListeners('save-file')
    }
  }, [])

  const loadFileTree = async (folderPath: string) => {
    try {
      const items = await window.electronAPI.readDirectory(folderPath)
      const processedItems = await Promise.all(items.map(async (item) => {
        if (item.isDirectory) {
          return { ...item, children: [], isExpanded: false }
        }
        return item
      }))
      setFileTree(processedItems)
    } catch (error) {
      console.error('Error loading file tree:', error)
    }
  }

  const loadSubDirectory = async (dirPath: string): Promise<FileItem[]> => {
    try {
      const items = await window.electronAPI.readDirectory(dirPath)
      return items.map(item => ({
        ...item,
        children: item.isDirectory ? [] : undefined,
        isExpanded: false
      }))
    } catch (error) {
      console.error('Error loading subdirectory:', error)
      return []
    }
  }

  const openFile = async (filePath: string) => {
    // 检查文件是否已经打开
    const existingIndex = openFiles.findIndex(file => file.path === filePath)
    if (existingIndex !== -1) {
      setActiveFileIndex(existingIndex)
      return
    }

    try {
      const content = await window.electronAPI.readFile(filePath)
      const fileName = filePath.split('/').pop() || 'Untitled'
      
      const newFile: OpenFile = {
        path: filePath,
        name: fileName,
        content,
        isDirty: false,
        language: getLanguageFromExtension(fileName)
      }

      setOpenFiles(prev => [...prev, newFile])
      setActiveFileIndex(openFiles.length)
    } catch (error) {
      console.error('Error opening file:', error)
    }
  }

  const closeFile = (index: number) => {
    setOpenFiles(prev => prev.filter((_, i) => i !== index))
    if (activeFileIndex === index) {
      setActiveFileIndex(Math.max(0, index - 1))
    } else if (activeFileIndex > index) {
      setActiveFileIndex(activeFileIndex - 1)
    }
  }

  const updateFileContent = (index: number, content: string) => {
    setOpenFiles(prev => prev.map((file, i) => 
      i === index ? { ...file, content, isDirty: true } : file
    ))
  }

  const saveCurrentFile = async () => {
    if (activeFileIndex >= 0 && activeFileIndex < openFiles.length) {
      const file = openFiles[activeFileIndex]
      try {
        await window.electronAPI.writeFile(file.path, file.content)
        setOpenFiles(prev => prev.map((f, i) => 
          i === activeFileIndex ? { ...f, isDirty: false } : f
        ))
      } catch (error) {
        console.error('Error saving file:', error)
      }
    }
  }

  const createNewFile = () => {
    const newFile: OpenFile = {
      path: '',
      name: 'Untitled',
      content: '',
      isDirty: true,
      language: 'plaintext'
    }
    setOpenFiles(prev => [...prev, newFile])
    setActiveFileIndex(openFiles.length)
  }

  const getLanguageFromExtension = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'json': 'json',
      'xml': 'xml',
      'md': 'markdown',
      'yml': 'yaml',
      'yaml': 'yaml'
    }
    return languageMap[ext || ''] || 'plaintext'
  }

  return (
    <AppContainer>
      <LeftPanel>
        <FileTree
          fileTree={fileTree}
          currentFolder={currentFolder}
          onFileSelect={openFile}
          onFolderExpand={loadSubDirectory}
          onUpdateTree={setFileTree}
        />
      </LeftPanel>
      
      <MiddlePanel>
        <CodeEditor
          openFiles={openFiles}
          activeFileIndex={activeFileIndex}
          onFileSelect={setActiveFileIndex}
          onFileClose={closeFile}
          onContentChange={updateFileContent}
        />
      </MiddlePanel>
      
      <RightPanel>
        <AIAssistant
          currentFile={activeFileIndex >= 0 ? openFiles[activeFileIndex] : null}
        />
      </RightPanel>
    </AppContainer>
  )
}

export default App
