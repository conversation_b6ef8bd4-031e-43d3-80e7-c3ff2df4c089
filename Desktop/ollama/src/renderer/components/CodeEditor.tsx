import React, { useRef, useEffect } from 'react'
import styled from 'styled-components'
import Editor from '@monaco-editor/react'
import { FaTimes, FaCircle } from 'react-icons/fa'
import { OpenFile } from '../types'

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`

const TabBar = styled.div`
  display: flex;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  overflow-x: auto;
  min-height: 35px;
`

const Tab = styled.div<{ isActive: boolean; isDirty: boolean }>`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${props => props.isActive ? '#1e1e1e' : '#2d2d30'};
  border-right: 1px solid #3e3e42;
  cursor: pointer;
  font-size: 13px;
  min-width: 120px;
  max-width: 200px;
  position: relative;
  
  &:hover {
    background-color: ${props => props.isActive ? '#1e1e1e' : '#37373d'};
  }
  
  &:hover .close-button {
    opacity: 1;
  }
`

const TabTitle = styled.span`
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
`

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  padding: 2px;
  opacity: 0;
  transition: opacity 0.2s;
  
  &:hover {
    background-color: #5a5a5a;
    border-radius: 2px;
  }
`

const DirtyIndicator = styled.div`
  position: absolute;
  top: 50%;
  right: 6px;
  transform: translateY(-50%);
  color: #ffffff;
`

const EditorWrapper = styled.div`
  flex: 1;
  background-color: #1e1e1e;
`

const EmptyState = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6a6a6a;
  font-size: 14px;
`

const StatusBar = styled.div`
  height: 24px;
  background-color: #007acc;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 12px;
  font-size: 12px;
  gap: 16px;
`

const StatusItem = styled.span`
  display: flex;
  align-items: center;
  gap: 4px;
`

interface CodeEditorProps {
  openFiles: OpenFile[]
  activeFileIndex: number
  onFileSelect: (index: number) => void
  onFileClose: (index: number) => void
  onContentChange: (index: number, content: string) => void
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  openFiles,
  activeFileIndex,
  onFileSelect,
  onFileClose,
  onContentChange
}) => {
  const editorRef = useRef<any>(null)
  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 })
  const [selectionInfo, setSelectionInfo] = useState({ selected: 0, total: 0 })

  useEffect(() => {
    if (editorRef.current && activeFileIndex >= 0) {
      const activeFile = openFiles[activeFileIndex]
      if (activeFile) {
        editorRef.current.setValue(activeFile.content)
      }
    }
  }, [activeFileIndex, openFiles])

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor

    // 配置编辑器选项
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'Menlo, Monaco, "Courier New", monospace',
      lineHeight: 1.5,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      suggestOnTriggerCharacters: true,
      quickSuggestions: true,
      parameterHints: { enabled: true },
      formatOnPaste: true,
      formatOnType: true,
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: true,
      folding: true,
      foldingStrategy: 'indentation',
      showFoldingControls: 'always',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true
      }
    })

    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // 触发保存
      window.electronAPI?.onSaveFile?.(() => {})
    })

    // 添加代码格式化快捷键
    editor.addCommand(monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.formatDocument').run()
    })

    // 监听光标位置变化
    editor.onDidChangeCursorPosition((e: any) => {
      setCursorPosition({
        line: e.position.lineNumber,
        column: e.position.column
      })
    })

    // 监听选择变化
    editor.onDidChangeCursorSelection((e: any) => {
      const model = editor.getModel()
      if (model) {
        const totalLines = model.getLineCount()
        const selectedText = model.getValueInRange(e.selection)
        setSelectionInfo({
          selected: selectedText.length,
          total: model.getValueLength()
        })
      }
    })
  }

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined && activeFileIndex >= 0) {
      onContentChange(activeFileIndex, value)
    }
  }

  const handleTabClick = (index: number, event: React.MouseEvent) => {
    event.stopPropagation()
    onFileSelect(index)
  }

  const handleCloseClick = (index: number, event: React.MouseEvent) => {
    event.stopPropagation()
    onFileClose(index)
  }

  const activeFile = activeFileIndex >= 0 ? openFiles[activeFileIndex] : null

  return (
    <EditorContainer>
      {openFiles.length > 0 && (
        <TabBar>
          {openFiles.map((file, index) => (
            <Tab
              key={`${file.path}-${index}`}
              isActive={index === activeFileIndex}
              isDirty={file.isDirty}
              onClick={(e) => handleTabClick(index, e)}
              className="tab"
            >
              <TabTitle>{file.name}</TabTitle>
              {file.isDirty ? (
                <DirtyIndicator>
                  <FaCircle size={6} />
                </DirtyIndicator>
              ) : (
                <CloseButton
                  className="close-button"
                  onClick={(e) => handleCloseClick(index, e)}
                >
                  <FaTimes size={10} />
                </CloseButton>
              )}
            </Tab>
          ))}
        </TabBar>
      )}
      
      <EditorWrapper>
        {activeFile ? (
          <Editor
            height="100%"
            language={activeFile.language}
            value={activeFile.content}
            theme="vs-dark"
            onChange={handleEditorChange}
            onMount={handleEditorDidMount}
            options={{
              selectOnLineNumbers: true,
              roundedSelection: false,
              readOnly: false,
              cursorStyle: 'line',
              automaticLayout: true,
            }}
          />
        ) : (
          <EmptyState>
            Open a file to start editing
          </EmptyState>
        )}
      </EditorWrapper>

      {activeFile && (
        <StatusBar>
          <StatusItem>
            {activeFile.language.toUpperCase()}
          </StatusItem>
          <StatusItem>
            Ln {cursorPosition.line}, Col {cursorPosition.column}
          </StatusItem>
          {selectionInfo.selected > 0 && (
            <StatusItem>
              ({selectionInfo.selected} selected)
            </StatusItem>
          )}
          <StatusItem>
            {activeFile.isDirty ? '● Modified' : 'Saved'}
          </StatusItem>
        </StatusBar>
      )}
    </EditorContainer>
  )
}

export default CodeEditor
