import React, { useState, useEffect, useRef } from 'react'
import styled from 'styled-components'
import { FaPaperPlane, FaRobot, FaUser, FaCog, FaCode, FaLightbulb, FaBug, FaComments, FaCopy, FaRedo } from 'react-icons/fa'
import { OpenFile, ChatMessage, OllamaModel } from '../types'
import { ollamaService } from '../services/ollamaService'

const AssistantContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #252526;
`

const Header = styled.div`
  padding: 12px 16px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const Title = styled.h3`
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #cccccc;
`

const SettingsButton = styled.button`
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  
  &:hover {
    background-color: #37373d;
  }
`

const ModelSelector = styled.select`
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #5a5a5a;
  border-radius: 3px;
  padding: 4px 8px;
  font-size: 12px;
  margin-bottom: 8px;
  width: 100%;
`

const ChatContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
`

const Message = styled.div<{ isUser: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 100%;
`

const MessageIcon = styled.div<{ isUser: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${props => props.isUser ? '#0078d4' : '#6b46c1'};
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
`

const MessageContent = styled.div`
  background-color: #3c3c3c;
  border-radius: 8px;
  padding: 12px;
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  position: relative;

  &:hover .message-actions {
    opacity: 1;
  }
`

const MessageActions = styled.div`
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
`

const MessageActionButton = styled.button`
  background-color: rgba(0, 0, 0, 0.5);
  color: #cccccc;
  border: none;
  border-radius: 3px;
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
`

const InputContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #3e3e42;
  background-color: #2d2d30;
`

const InputWrapper = styled.div`
  display: flex;
  gap: 8px;
  align-items: flex-end;
`

const TextArea = styled.textarea`
  flex: 1;
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #5a5a5a;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  font-family: inherit;
  resize: none;
  min-height: 36px;
  max-height: 120px;
  
  &:focus {
    outline: none;
    border-color: #0078d4;
  }
  
  &::placeholder {
    color: #6a6a6a;
  }
`

const SendButton = styled.button`
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 36px;
  
  &:hover:not(:disabled) {
    background-color: #106ebe;
  }
  
  &:disabled {
    background-color: #5a5a5a;
    cursor: not-allowed;
  }
`

const ConnectionStatus = styled.div<{ isConnected: boolean }>`
  font-size: 11px;
  color: ${props => props.isConnected ? '#4caf50' : '#f44336'};
  margin-bottom: 8px;
`

const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6a6a6a;
  font-size: 12px;
  padding: 8px 12px;
`

const QuickActions = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`

const QuickActionButton = styled.button`
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #5a5a5a;
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 11px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    background-color: #4a4a4a;
    border-color: #0078d4;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

interface AIAssistantProps {
  currentFile: OpenFile | null
}

const AIAssistant: React.FC<AIAssistantProps> = ({ currentFile }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [availableModels, setAvailableModels] = useState<OllamaModel[]>([])
  const [selectedModel, setSelectedModel] = useState('')
  const [streamingMessage, setStreamingMessage] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    checkOllamaConnection()
    loadAvailableModels()
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const checkOllamaConnection = async () => {
    const connected = await ollamaService.checkConnection()
    setIsConnected(connected)
  }

  const loadAvailableModels = async () => {
    try {
      const models = await ollamaService.getModels()
      setAvailableModels(models)
      if (models.length > 0 && !selectedModel) {
        setSelectedModel(models[0].name)
      }
    } catch (error) {
      console.error('Error loading models:', error)
    }
  }

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }

  const sendMessage = async (customPrompt?: string) => {
    const messageContent = customPrompt || inputValue.trim()
    if (!messageContent || isLoading || !selectedModel) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: messageContent,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    if (!customPrompt) setInputValue('')
    setIsLoading(true)
    setIsStreaming(true)
    setStreamingMessage('')

    // Add a placeholder message for streaming
    const assistantMessageId = (Date.now() + 1).toString()
    const placeholderMessage: ChatMessage = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, placeholderMessage])

    try {
      // 构建上下文信息
      let contextInfo = ''
      if (currentFile) {
        contextInfo = `Current file: ${currentFile.name} (${currentFile.language})\n\nFile content:\n${currentFile.content}\n\n`
      }

      let fullResponse = ''
      const response = await ollamaService.generateResponse(
        selectedModel,
        messageContent,
        contextInfo,
        (chunk: string) => {
          fullResponse += chunk
          setStreamingMessage(fullResponse)
          // Update the placeholder message
          setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, content: fullResponse }
              : msg
          ))
        }
      )

      // Final update
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, content: response || fullResponse }
          : msg
      ))
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage = 'Sorry, I encountered an error while processing your request. Please make sure Ollama is running.'
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? { ...msg, content: errorMessage }
          : msg
      ))
    } finally {
      setIsLoading(false)
      setIsStreaming(false)
      setStreamingMessage('')
    }
  }

  const handleQuickAction = async (action: string) => {
    if (!currentFile || !selectedModel) return

    const prompts: { [key: string]: string } = {
      explain: `Please explain this ${currentFile.language} code in detail.`,
      review: `Please review this ${currentFile.language} code and suggest improvements.`,
      debug: `Please help debug this ${currentFile.language} code and identify potential issues.`,
      comment: `Please add appropriate comments to this ${currentFile.language} code.`
    }

    if (prompts[action]) {
      await sendMessage(prompts[action])
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const regenerateResponse = async (messageIndex: number) => {
    if (messageIndex < 1 || messageIndex >= messages.length) return

    const userMessage = messages[messageIndex - 1]
    if (userMessage.role !== 'user') return

    // Remove the assistant message and regenerate
    setMessages(prev => prev.slice(0, messageIndex))
    await sendMessage(userMessage.content)
  }

  return (
    <AssistantContainer>
      <Header>
        <Title>AI Assistant</Title>
        <SettingsButton onClick={checkOllamaConnection}>
          <FaCog size={14} />
        </SettingsButton>
      </Header>

      <div style={{ padding: '12px 16px 0' }}>
        <ConnectionStatus isConnected={isConnected}>
          {isConnected ? '● Connected to Ollama' : '● Disconnected from Ollama'}
        </ConnectionStatus>
        
        <ModelSelector
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          disabled={!isConnected}
        >
          <option value="">Select a model...</option>
          {availableModels.map(model => (
            <option key={model.id} value={model.name}>
              {model.name}
            </option>
          ))}
        </ModelSelector>
      </div>

      {currentFile && (
        <QuickActions>
          <QuickActionButton
            onClick={() => handleQuickAction('explain')}
            disabled={isLoading}
          >
            <FaLightbulb size={10} />
            Explain Code
          </QuickActionButton>
          <QuickActionButton
            onClick={() => handleQuickAction('review')}
            disabled={isLoading}
          >
            <FaCode size={10} />
            Review Code
          </QuickActionButton>
          <QuickActionButton
            onClick={() => handleQuickAction('debug')}
            disabled={isLoading}
          >
            <FaBug size={10} />
            Debug Code
          </QuickActionButton>
          <QuickActionButton
            onClick={() => handleQuickAction('comment')}
            disabled={isLoading}
          >
            <FaComments size={10} />
            Add Comments
          </QuickActionButton>
        </QuickActions>
      )}

      <ChatContainer ref={chatContainerRef}>
        {messages.length === 0 && (
          <MessageContent style={{ backgroundColor: 'transparent', padding: 0 }}>
            <div style={{ textAlign: 'center', color: '#6a6a6a' }}>
              Start a conversation with your AI assistant!
              {currentFile && (
                <div style={{ marginTop: '8px', fontSize: '12px' }}>
                  Current file: {currentFile.name}
                </div>
              )}
            </div>
          </MessageContent>
        )}
        
        {messages.map((message, index) => (
          <Message key={message.id} isUser={message.role === 'user'}>
            <MessageIcon isUser={message.role === 'user'}>
              {message.role === 'user' ? (
                <FaUser size={12} color="white" />
              ) : (
                <FaRobot size={12} color="white" />
              )}
            </MessageIcon>
            <MessageContent>
              {message.content}
              {message.role === 'assistant' && message.content && (
                <MessageActions className="message-actions">
                  <MessageActionButton
                    onClick={() => copyToClipboard(message.content)}
                    title="Copy message"
                  >
                    <FaCopy size={10} />
                  </MessageActionButton>
                  <MessageActionButton
                    onClick={() => regenerateResponse(index)}
                    title="Regenerate response"
                    disabled={isLoading}
                  >
                    <FaRedo size={10} />
                  </MessageActionButton>
                </MessageActions>
              )}
            </MessageContent>
          </Message>
        ))}
        
        {isLoading && (
          <LoadingIndicator>
            <FaRobot size={12} />
            AI is thinking...
          </LoadingIndicator>
        )}
      </ChatContainer>

      <InputContainer>
        <InputWrapper>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about your code or request help..."
            disabled={isLoading || !isConnected || !selectedModel}
          />
          <SendButton
            onClick={sendMessage}
            disabled={isLoading || !inputValue.trim() || !isConnected || !selectedModel}
          >
            <FaPaperPlane size={12} />
          </SendButton>
        </InputWrapper>
      </InputContainer>
    </AssistantContainer>
  )
}

export default AIAssistant
