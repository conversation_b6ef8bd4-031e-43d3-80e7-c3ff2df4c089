import React, { useState, useRef, useEffect } from 'react'
import styled from 'styled-components'
import { FaFolder, FaFolderOpen, FaFile, FaChevronRight, FaChevronDown, FaPlus, FaTrash, FaEdit } from 'react-icons/fa'
import { FileItem } from '../types'

const TreeContainer = styled.div`
  height: 100%;
  overflow-y: auto;
  padding: 8px;
`

const TreeHeader = styled.div`
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  color: #cccccc;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
`

const TreeItem = styled.div<{ depth: number; isSelected?: boolean }>`
  display: flex;
  align-items: center;
  padding: 4px 8px;
  padding-left: ${props => 8 + props.depth * 16}px;
  cursor: pointer;
  font-size: 13px;
  background-color: ${props => props.isSelected ? '#094771' : 'transparent'};
  
  &:hover {
    background-color: ${props => props.isSelected ? '#094771' : '#2a2d2e'};
  }
`

const IconContainer = styled.span`
  margin-right: 6px;
  display: flex;
  align-items: center;
  width: 16px;
  justify-content: center;
`

const FileName = styled.span`
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

const ChevronIcon = styled.span`
  margin-right: 4px;
  width: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
`

const ContextMenu = styled.div<{ x: number; y: number; visible: boolean }>`
  position: fixed;
  top: ${props => props.y}px;
  left: ${props => props.x}px;
  background-color: #3c3c3c;
  border: 1px solid #5a5a5a;
  border-radius: 4px;
  padding: 4px 0;
  z-index: 1000;
  display: ${props => props.visible ? 'block' : 'none'};
  min-width: 150px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
`

const ContextMenuItem = styled.div`
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #cccccc;

  &:hover {
    background-color: #094771;
  }
`

const InputField = styled.input`
  background-color: #3c3c3c;
  color: #cccccc;
  border: 1px solid #0078d4;
  border-radius: 2px;
  padding: 2px 4px;
  font-size: 13px;
  width: 100%;

  &:focus {
    outline: none;
  }
`

interface FileTreeProps {
  fileTree: FileItem[]
  currentFolder: string
  onFileSelect: (filePath: string) => void
  onFolderExpand: (folderPath: string) => Promise<FileItem[]>
  onUpdateTree: (tree: FileItem[]) => void
}

const FileTree: React.FC<FileTreeProps> = ({
  fileTree,
  currentFolder,
  onFileSelect,
  onFolderExpand,
  onUpdateTree
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [selectedItem, setSelectedItem] = useState<string>('')
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; visible: boolean; targetPath: string }>({
    x: 0, y: 0, visible: false, targetPath: ''
  })
  const [editingItem, setEditingItem] = useState<string>('')
  const [newItemName, setNewItemName] = useState<string>('')
  const [isCreatingNew, setIsCreatingNew] = useState<{ type: 'file' | 'folder' | null; parentPath: string }>({
    type: null, parentPath: ''
  })
  const contextMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setContextMenu({ x: 0, y: 0, visible: false, targetPath: '' })
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const updateTreeItem = (tree: FileItem[], targetPath: string, children: FileItem[]): FileItem[] => {
    return tree.map(item => {
      if (item.path === targetPath) {
        return { ...item, children, isExpanded: true }
      } else if (item.children) {
        return { ...item, children: updateTreeItem(item.children, targetPath, children) }
      }
      return item
    })
  }

  const toggleFolder = async (folderPath: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath)
    } else {
      newExpanded.add(folderPath)
      const children = await onFolderExpand(folderPath)
      const updatedTree = updateTreeItem(fileTree, folderPath, children)
      onUpdateTree(updatedTree)
    }
    setExpandedFolders(newExpanded)
  }

  const handleItemClick = (item: FileItem) => {
    setSelectedItem(item.path)
    if (item.isDirectory) {
      toggleFolder(item.path)
    } else {
      onFileSelect(item.path)
    }
  }

  const handleContextMenu = (event: React.MouseEvent, item: FileItem) => {
    event.preventDefault()
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      visible: true,
      targetPath: item.path
    })
  }

  const handleCreateNew = (type: 'file' | 'folder') => {
    setIsCreatingNew({ type, parentPath: contextMenu.targetPath })
    setNewItemName('')
    setContextMenu({ x: 0, y: 0, visible: false, targetPath: '' })
  }

  const handleRename = () => {
    setEditingItem(contextMenu.targetPath)
    const item = findItemByPath(fileTree, contextMenu.targetPath)
    setNewItemName(item?.name || '')
    setContextMenu({ x: 0, y: 0, visible: false, targetPath: '' })
  }

  const handleDelete = async () => {
    // TODO: Implement delete functionality
    console.log('Delete:', contextMenu.targetPath)
    setContextMenu({ x: 0, y: 0, visible: false, targetPath: '' })
  }

  const findItemByPath = (tree: FileItem[], targetPath: string): FileItem | null => {
    for (const item of tree) {
      if (item.path === targetPath) {
        return item
      }
      if (item.children) {
        const found = findItemByPath(item.children, targetPath)
        if (found) return found
      }
    }
    return null
  }

  const renderFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const iconColor = getFileIconColor(ext || '')
    return <FaFile color={iconColor} size={12} />
  }

  const getFileIconColor = (extension: string): string => {
    const colorMap: { [key: string]: string } = {
      'js': '#f7df1e',
      'jsx': '#61dafb',
      'ts': '#3178c6',
      'tsx': '#61dafb',
      'py': '#3776ab',
      'java': '#ed8b00',
      'cpp': '#00599c',
      'c': '#a8b9cc',
      'cs': '#239120',
      'php': '#777bb4',
      'rb': '#cc342d',
      'go': '#00add8',
      'rs': '#dea584',
      'html': '#e34f26',
      'css': '#1572b6',
      'scss': '#cf649a',
      'json': '#ffd700',
      'xml': '#ff6600',
      'md': '#083fa1',
      'yml': '#cb171e',
      'yaml': '#cb171e'
    }
    return colorMap[extension] || '#d4d4d4'
  }

  const renderTreeItem = (item: FileItem, depth: number = 0): React.ReactNode => {
    const isExpanded = expandedFolders.has(item.path)
    const isSelected = selectedItem === item.path
    const isEditing = editingItem === item.path

    return (
      <div key={item.path}>
        <TreeItem
          depth={depth}
          isSelected={isSelected}
          onClick={() => handleItemClick(item)}
          onContextMenu={(e) => handleContextMenu(e, item)}
        >
          {item.isDirectory && (
            <ChevronIcon>
              {isExpanded ? (
                <FaChevronDown size={10} />
              ) : (
                <FaChevronRight size={10} />
              )}
            </ChevronIcon>
          )}

          <IconContainer>
            {item.isDirectory ? (
              isExpanded ? (
                <FaFolderOpen color="#dcb67a" size={12} />
              ) : (
                <FaFolder color="#dcb67a" size={12} />
              )
            ) : (
              renderFileIcon(item.name)
            )}
          </IconContainer>

          {isEditing ? (
            <InputField
              value={newItemName}
              onChange={(e) => setNewItemName(e.target.value)}
              onBlur={() => setEditingItem('')}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  // TODO: Implement rename functionality
                  console.log('Rename to:', newItemName)
                  setEditingItem('')
                }
                if (e.key === 'Escape') {
                  setEditingItem('')
                }
              }}
              autoFocus
            />
          ) : (
            <FileName>{item.name}</FileName>
          )}
        </TreeItem>

        {item.isDirectory && isExpanded && item.children && (
          <>
            {item.children.map(child => renderTreeItem(child, depth + 1))}
            {isCreatingNew.type && isCreatingNew.parentPath === item.path && (
              <TreeItem depth={depth + 1} isSelected={false}>
                <IconContainer>
                  {isCreatingNew.type === 'folder' ? (
                    <FaFolder color="#dcb67a" size={12} />
                  ) : (
                    <FaFile color="#d4d4d4" size={12} />
                  )}
                </IconContainer>
                <InputField
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  onBlur={() => setIsCreatingNew({ type: null, parentPath: '' })}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      // TODO: Implement create functionality
                      console.log('Create:', isCreatingNew.type, newItemName)
                      setIsCreatingNew({ type: null, parentPath: '' })
                    }
                    if (e.key === 'Escape') {
                      setIsCreatingNew({ type: null, parentPath: '' })
                    }
                  }}
                  placeholder={`New ${isCreatingNew.type}...`}
                  autoFocus
                />
              </TreeItem>
            )}
          </>
        )}
      </div>
    )
  }

  const folderName = currentFolder ? currentFolder.split('/').pop() : 'No Folder Open'

  return (
    <TreeContainer>
      <TreeHeader>{folderName}</TreeHeader>
      {fileTree.map(item => renderTreeItem(item))}

      <ContextMenu
        ref={contextMenuRef}
        x={contextMenu.x}
        y={contextMenu.y}
        visible={contextMenu.visible}
      >
        <ContextMenuItem onClick={() => handleCreateNew('file')}>
          <FaFile size={12} />
          New File
        </ContextMenuItem>
        <ContextMenuItem onClick={() => handleCreateNew('folder')}>
          <FaFolder size={12} />
          New Folder
        </ContextMenuItem>
        <ContextMenuItem onClick={handleRename}>
          <FaEdit size={12} />
          Rename
        </ContextMenuItem>
        <ContextMenuItem onClick={handleDelete}>
          <FaTrash size={12} />
          Delete
        </ContextMenuItem>
      </ContextMenu>
    </TreeContainer>
  )
}

export default FileTree
